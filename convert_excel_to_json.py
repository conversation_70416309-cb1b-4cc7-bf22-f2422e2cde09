import pandas as pd
import json

def excel_to_json(file_path):
    # <PERSON><PERSON><PERSON> t<PERSON> cả các sheet trong file Excel
    xls = pd.ExcelFile(file_path)
    
    result = {}
    
    for sheet_name in xls.sheet_names:
        # <PERSON><PERSON><PERSON> dữ liệu từng sheet
        df = pd.read_excel(xls, sheet_name=sheet_name)
        
        # Chu<PERSON>n hóa tên cột
        df.columns = df.columns.str.strip().str.upper()
        json_data = df.to_json(orient="records")
        # Lọc và xử lý dữ liệu
        records = []
        for item in json.loads(json_data):
            records.append(item)
        # for _, row in df.iterrows():
        #     # Xử lý riêng cho sheet ACCESSOWNER
        #     if sheet_name == 'ACCESSOWNER':
        #         if pd.isna(row.get('A')) or row['A'] == '':
        #             continue
        #         records.append({
        #             'code': str(row['A']).strip(),
        #             'decode': str(row['B']).strip() if pd.notna(row.get('B')) else ''
        #         })
        #     else:
                
        #         if pd.isna(row.get('A')) or row['A'] == '':
        #             continue
        #         records.append({
        #             'code': str(row['A']).strip(),
        #             'decode': str(row['B']).strip() if pd.notna(row.get('B')) else ''
        #         })
        
        result[sheet_name] = records
    
    return result

# Thực thi chương trình
if __name__ == "__main__":
    input_file = '/Users/<USER>/Downloads/code-decode.xlsx'
    output_file = 'code-decode.json'
    
    # Convert Excel to JSON
    json_data = excel_to_json(input_file)
    
    # Lưu file JSON
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(json_data, f, ensure_ascii=False, indent=2)
    
    print(f'Conversion completed! File saved as {output_file}')