{"name": "crypto-es", "version": "2.1.0", "description": "A cryptography algorithms library compatible with ES6 and TypeScript", "keywords": ["typescript", "security", "crypto", "cipher", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TypedArray", "file", "ECMAScript", "ES6", "Hash", "MD5", "SHA1", "SHA-1", "SHA2", "SHA-2", "SHA3", "SHA-3", "SHA256", "SHA-256", "RC4", "Rabbit", "AES", "DES", "3DES", "TripleDES", "PBKDF2", "HMAC", "HEX", "Base64", "Base64url", "blowfish"], "sideEffects": false, "type": "module", "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib"], "scripts": {"test": "node --experimental-vm-modules node_modules/jest/bin/jest.js", "debug": "node --experimental-modules __tests__/debug.js"}, "repository": {"type": "git", "url": "git+https://github.com/entronad/crypto-es.git"}, "author": "LIN Chen", "license": "MIT", "bugs": {"url": "https://github.com/entronad/crypto-es/issues"}, "homepage": "https://github.com/entronad/crypto-es#readme", "devDependencies": {"@types/jest": "^29.5.2", "jest": "29.5.0", "ts-jest": "^29.1.1", "typescript": "^5.1.6"}, "jest": {"testRegex": "(/__tests__/.*\\.test\\.ts)$", "preset": "ts-jest/presets/default-esm"}}