import CryptoES from 'crypto-es';
import md5 from 'md5';


export const encryptPem = (
  plainData,
  u
) => {
  const j = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.5S87kehv0W3NMQRh4CIG5s3yjsQ0tUpXw5NLLSc7zs0";
  const k = md5(((j || '').split('.').pop() || '') + u);
  const key = CryptoES.enc.Hex.parse(k);
  const iv = CryptoES.enc.Hex.parse(k);
  const mode = CryptoES.mode.CBC;
  const padding = CryptoES.pad.Pkcs7;
  const options = { iv: iv, mode: mode, padding: padding };
  return encodeURIComponent(CryptoES.AES.encrypt(plainData, key, options).toString());
};

// const decryptPem = (encryptedData, u) => {
//   encryptedData = decodeURIComponent(encryptedData);
//   console.log(encryptedData);
//   const j = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.5S87kehv0W3NMQRh4CIG5s3yjsQ0tUpXw5NLLSc7zs0";
//   const k = md5(((j || '').split('.').pop() || '') + u).substring(0, 24);
//   console.log(k);
//   const key = CryptoES.enc.Hex.parse(k);
//   console.log(key);
//   const iv = CryptoES.enc.Hex.parse(k);
//   const mode = CryptoES.mode.CBC;
//   const padding = CryptoES.pad.Pkcs7;
//   const options = { iv: iv, mode: mode, padding: padding };
//   return CryptoES.AES.decrypt(encryptedData, key, options).toString(
//     CryptoES.enc.Utf8
//   );
// };

// console.log(decryptPem('cRU%2BNwmZEdOc9TlP9BEPMB47iZOhapu6WiKbQZZShEeYEnLDz%2Bu5DdQEOYEfgqcTSjHXhJvYydgd623RsMhf6QwXBlLkRyBzI2w6Tu0cTuEy%2BkHGGgPnXgPV9IaMo8sCCD3tK072QwzdDgxiVcAYl5QqjN1YrsvhiUaW%2F3MfB8S8aMwtYcV5VuHpxV%2BWPbMx', 'https://release.mobio.vn/adm/api/v2.1/merchants/57d559c1-39a1-4cee-b024-b953428b5ac8/public-configs'));


console.log(encryptPem('https://release.mobio.vn/adm/api/v2.1/merchants/57d559c1-39a1-4cee-b024-b953428b5ac8/public-configs::Y_R9m02Iwd4C5j1?NGYAe28-MpnoLSWQ:T@<=q]t3zDhbG>i0>%]?kChYXZD[>I/j:other:jh(3*1P1xXG0F>r9*CP7m3}@w>TV&XDq', 'https://release.mobio.vn/adm/api/v2.1/merchants/57d559c1-39a1-4cee-b024-b953428b5ac8/public-configs'));