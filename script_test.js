import http from 'k6/http';
import { sleep, check } from 'k6';

// Cấu hình test
export let options = {
    // Cách 1: Tăng số lượng VUs
    vus: 100,  // Tăng từ mức mặc định (thường là 1)

    // // Cách 2: Sử dụng stages để tăng dần số lượng VUs
    // stages: [
    //     { duration: '30s', target: 50 },   // Tăng lên 50 VUs trong 30s
    //     { duration: '1m', target: 200 },   // Tăng tiếp lên 200 VUs trong 1 phút
    //     { duration: '2m', target: 200 },   // Duy trì 200 VUs trong 2 phút
    //     { duration: '30s', target: 0 },    // Giảm dần về 0
    // ],

    // Cách 3: Sử dụng scenarios để điều khiển chi tiết hơn
    scenarios: {
        high_load: {
            executor: 'constant-arrival-rate',
            rate: 1000,                  // 1000 requests mỗi đơn vị thời gian
            timeUnit: '1s',              // đơn vị thời gian: giây
            duration: '5m',              // chạy trong 5 phút
            preAllocatedVUs: 100,        // số lượng VUs khởi tạo sẵn
            maxVUs: 500,                 // số lượng VUs tối đa có thể tạo nếu cần
        }
    }
};

export default function () {
    const url = 'https://release.mobio.vn/ticket/api/v1.0/tickets/actions/filter?per_page=30&lang=vi'; // Thay URL của bạn vào đây
    const payload = JSON.stringify({
        "ticket_filters": [
            {
                "criteria_key": "cri_assignment_status",
                "operator_key": "op_is_in",
                "values": [
                    "assigned"
                ]
            },
            {
                "criteria_key": "cri_type_ticket_id",
                "operator_key": "op_is_in",
                "values": [
                    "6237facab5e5e8626bace7c2"
                ]
            }
        ],
        "search": ""
    });

    const params = {
        headers: {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9,vi;q=0.8',
            'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Y4to39NlIdiZU4DG4VwKbQjJKo5Bld9e1X7_szl_phg',
            'Cache-Control': 'no-cache',
            'Client-Version': 'v4.39.0',
            'Connection': 'keep-alive',
            'Content-Type': 'application/json',
            'Origin': 'https://release.mobio.vn',
            'Pragma': 'no-cache',
            'Referer': 'https://release.mobio.vn/micro-sites/ticket/list/',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36',
            'X-Merchant-ID': '1b99bdcf-d582-4f49-9715-1b61dfff3924',
            'mobio-pem': 'KjJuUuY5aovEYopwLPC%2BP2%2FyhWqLfJafro0rI8WYPfGfKsQ0FyUFBhesLyUWu8FVD1698jDPnq%2By7YUtW7pKVg%3D%3D',
            'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'x-license-product': 'operation',
            'Cookie': '_ga=GA1.1.**********.**********; _ga_7LV741RE1F=GS1.1.**********.124.1.**********.0.0.0'
        },
    };

    // Gửi request
    const response = http.post(url, payload, params);

    // Kiểm tra response
    check(response, {
        'status là 200': (r) => r.status === 200,
        'status là 204': (r) => r.status === 204,
        'status là 429': (r) => r.status === 429,
        'response time < 500ms': (r) => r.timings.duration < 500,
    });
}