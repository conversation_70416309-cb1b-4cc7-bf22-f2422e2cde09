import re


def remove_csv_injection_chars(value):
    """
    Loại bỏ các ký tự có thể gây CSV Injection
    """
    if value is None or not isinstance(value, str):
        return value

    # <PERSON>ại bỏ các ký tự điều khiển
    value = re.sub(r"[\x00-\x1F\x7F]", "", value)

    # Loại bỏ các ký tự nguy hiểm ở đầu chuỗi
    value = re.sub(r"^[=+\-@\t\r\n]+", "", value)

    return value


if __name__ == "__main__":
    data = [
        {"name": "Alice", "comment": "=\tSUM(1+1)"},
        {"name": "<PERSON>", "comment": "@VLOOKUP(...)"},
        {"name": "<PERSON>", "comment": "Bình+thường-"},
    ]

    cleaned_data = [
        {"name": remove_csv_injection_chars(row["name"]), "comment": remove_csv_injection_chars(row["comment"])}
        for row in data
    ]

    print(cleaned_data)
