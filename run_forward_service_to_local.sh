#! /bin/bash

kubectl port-forward svc/admin-app-api-service 8081:80 -n mobio &
kubectl port-forward svc/company-app-api-internal-service 8084:80 -n mobio &
kubectl port-forward svc/pf-api-internal-dataflow-service 8085:80 -n mobio &
kubectl port-forward svc/tag-app-api-service 8086:80 -n mobio &
# kubectl port-forward svc/mobilebackendmockup-api-service 1234:80 -n mobio &
kubectl port-forward svc/profiling-v4-app-internal-api-service 8088:80 -n mobio &
kubectl port-forward svc/market-place-app-api-service 8089:80 -n mobio &
kubectl port-forward svc/wfb-internal-app-api-service 8090:80 -n mobio &
kubectl port-forward svc/ticket-app-api-service 8091:80 -n mobio &
kubectl port-forward svc/pf-api-internal-dataflow-service 8092:80 -n mobio &

wait

