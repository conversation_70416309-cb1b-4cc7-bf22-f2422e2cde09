import http from 'k6/http';
import { sleep, check } from 'k6';

// <PERSON><PERSON><PERSON> test
export let options = {
    stages: [
        { duration: '10s', target: 5 }, // Tăng dần lên 5 VUs trong 10s
        { duration: '20s', target: 5 }, // Giữ 5 VUs trong 20s
        { duration: '10s', target: 0 }, // Giảm về 0 VUs trong 10s
    ],
    thresholds: {
        http_req_failed: ['rate<0.1'], // Tỷ lệ lỗi phải dưới 10%
        http_req_duration: ['p(95)<500'], // 95% request phải dưới 500ms
    },
};

export default function () {
    const url = 'https://uat-mkt.hdbank.com.vn/sale/api/v1.0/deal/statistic/save-view?lang=vi'; // Thay URL của bạn vào đây
    const payload = JSON.stringify({
        "search": "",
        "save_view_code": "assigned",
        "sale_filters": [
            {
                "criteria_key": "cri_assignment_status",
                "operator_key": "op_is_in",
                "values": [
                    "assigned"
                ]
            }
        ]
    });

    const params = {
        headers: {
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "en-US,en;q=0.9",
            "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.B0DChDikchx4L3ZLk0Cf9TzBIYqVn_xBKEUyOHSObJs",
            "Client-Version": "v4.27.0",
            "Connection": "keep-alive",
            "Content-Type": "application/json",
            "DNT": "1",
            "Origin": "https://uat-mkt.hdbank.com.vn",
            "Referer": "https://uat-mkt.hdbank.com.vn/micro-sites/sale/list/",
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36",
            "X-Merchant-ID": "77cb79b8-ce0e-441d-8742-727cd5c10c25",
            "mobio-pem": "q9xSC%2BYo8O4ZvjOiu5dukHfCsn2D60ZpXhS%2FafpqrHMLCZzLXDlnmxE2d4JwnzS0aideHkQudFG1NxPTkc1SmQ%3D%3D",
            "sec-ch-ua": "\"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"macOS\"",
            "x-license-product": "operation"
        },
    };

    // Gửi request
    const response = http.post(url, payload, params);

    // Kiểm tra response
    check(response, {
        'status là 200': (r) => r.status === 200,
        'status là 204': (r) => r.status === 204,
        'status là 429': (r) => r.status === 429,
        'response time < 500ms': (r) => r.timings.duration < 500,
    });

    sleep(1 / 5); // Nghỉ giữa các request để duy trì 5 request/s
}