import base64
import os
from hashlib import sha256

from Crypto.Cipher import AES, DES3
from Crypto.Random import get_random_bytes
from Crypto.Util.Padding import pad, unpad

# --- Chuyển đổi hàm EncryptOTP (Sử dụng 3DES ECB) ---


def encrypt_otp(encryption_key_str: str, to_encrypt_str: str) -> str:
    """
    Mã hóa một chuỗi bằng TripleDES (3DES) ở chế độ ECB với PKCS7 padding.

    LƯU Ý: Chế độ ECB không an toàn và không được khuyến khích.
          Việc sử dụng trực tiếp byte UTF-8 của chuỗi khóa có thể không an toàn
          và có thể không tạo ra khóa có độ dài hợp lệ (16 hoặc 24 byte).
          Xem xét việc băm khóa gốc để có độ dài phù hợp.
    """
    try:
        key_bytes = encryption_key_str.encode("utf-8")
        to_encrypt_bytes = to_encrypt_str.encode("utf-8")

        # Kiểm tra và điều chỉnh độ dài khóa (ví dụ đơn giản, nên dùng hash)
        # DES3 yêu cầu khóa 16 hoặc 24 byte.
        # Cách làm tốt hơn: key_bytes = sha256(encryption_key_str.encode('utf-8')).digest()[:24] # Hoặc [:16]
        if len(key_bytes) not in (16, 24):
            # Sử dụng SHA-256 để tạo khóa 24 byte làm ví dụ an toàn hơn
            print("Cảnh báo: Độ dài khóa gốc không hợp lệ cho 3DES. Sử dụng SHA-256 hash (24 byte).")
            key_bytes = sha256(key_bytes).digest()[:24]
            # Hoặc nếu muốn báo lỗi:
            # raise ValueError("Độ dài khóa phải là 16 hoặc 24 byte cho 3DES.")

        cipher = DES3.new(key_bytes, DES3.MODE_ECB)

        # Áp dụng PKCS7 padding trước khi mã hóa
        padded_data = pad(to_encrypt_bytes, DES3.block_size, style="pkcs7")

        encrypted_bytes = cipher.encrypt(padded_data)

        # Trả về chuỗi Base64
        return base64.b64encode(encrypted_bytes).decode("utf-8")

    except Exception as e:
        print(f"Lỗi khi mã hóa OTP: {e}")
        # Trong C#, lỗi TransformFinalBlock có thể xảy ra nếu padding sai,
        # pycryptodome xử lý padding riêng biệt.
        raise  # Hoặc trả về giá trị lỗi thích hợp


# --- Chuyển đổi lớp EncryptUtil (Sử dụng AES CTR) ---


class EncryptUtil:
    _IV_LENGTH = 16  # AES thường dùng IV 16 byte (128 bit)

    @staticmethod
    def _generate_iv() -> bytes:
        """Tạo IV ngẫu nhiên an toàn về mặt mật mã."""
        return get_random_bytes(EncryptUtil._IV_LENGTH)

    # Các hàm GetIV và RemoveTagAndIV dùng cho giải mã, không cần cho mã hóa này
    # def get_iv(arr: bytes) -> bytes:
    #     return arr[:EncryptUtil._IV_LENGTH]
    #
    # def remove_tag_and_iv(arr: bytes) -> bytes:
    #     return arr[EncryptUtil._IV_LENGTH:]

    @staticmethod
    def _encrypt_data(encryption_key_str: str, input_bytes: bytes, iv: bytes) -> bytes:
        """Hàm nội bộ để mã hóa dữ liệu bằng AES CTR."""
        try:
            # Sử dụng UTF-8 cho key (nhất quán với hàm đầu)
            key_bytes = encryption_key_str.encode("utf-8")

            # Kiểm tra và điều chỉnh độ dài khóa (16, 24, hoặc 32 byte cho AES)
            # Cách làm tốt hơn: key_bytes = sha256(encryption_key_str.encode('utf-8')).digest() # 32 byte
            valid_key_lengths = (16, 24, 32)
            if len(key_bytes) not in valid_key_lengths:
                # Sử dụng SHA-256 để tạo khóa 32 byte (AES-256) làm ví dụ an toàn hơn
                print("Cảnh báo: Độ dài khóa gốc không hợp lệ cho AES. Sử dụng SHA-256 hash (32 byte).")
                key_bytes = sha256(key_bytes).digest()
                # Hoặc nếu muốn báo lỗi:
                # raise ValueError("Độ dài khóa phải là 16, 24, hoặc 32 byte cho AES.")
            elif len(key_bytes) == 24:
                print("Cảnh báo: Khóa 24 byte (192-bit) cho AES ít phổ biến hơn 16 hoặc 32 byte.")

            # AES CTR không cần padding
            # Thuật toán trong C# là "AES/CTR/NoPadding"
            cipher = AES.new(key_bytes, AES.MODE_CTR, nonce=iv)  # nonce chính là IV trong CTR mode
            encrypted_bytes = cipher.encrypt(input_bytes)
            return encrypted_bytes
        except Exception as e:
            print(f"Lỗi trong _encrypt_data: {e}")
            raise

    @staticmethod
    def encrypt(encryption_key_str: str, data_str: str) -> str:
        """
        Mã hóa dữ liệu bằng AES CTR, nối IV vào đầu và trả về Base64.
        """
        try:
            text_data_bytes = data_str.encode("utf-8")

            # Tạo IV ngẫu nhiên an toàn
            iv = EncryptUtil._generate_iv()

            # Mã hóa dữ liệu
            encrypted_request = EncryptUtil._encrypt_data(encryption_key_str, text_data_bytes, iv)

            # Nối IV vào đầu dữ liệu đã mã hóa (IV || Ciphertext)
            encrypted_bytes_and_iv = iv + encrypted_request

            # Trả về chuỗi Base64
            return base64.b64encode(encrypted_bytes_and_iv).decode("utf-8")
        except Exception as e:
            print(f"Lỗi khi mã hóa (EncryptUtil): {e}")
            raise


# --- Ví dụ sử dụng ---
if __name__ == "__main__":
    # --- Ví dụ cho EncryptOTP (3DES ECB) ---
    otp_key = "B89vl8144vwFOHEb"  # Nên là 16 hoặc 24 byte sau khi encode UTF-8
    # Hoặc dùng hash: otp_key = "some_secret_phrase"
    otp_data = "123456"

    # Đảm bảo key hợp lệ (ví dụ dùng hash)
    hashed_otp_key_24_bytes = sha256(otp_key.encode("utf-8")).digest()[:24]

    print(f"OTP Key (UTF-8 bytes): {otp_key.encode('utf-8')}")
    print(f"OTP Key (Hashed 24 bytes): {hashed_otp_key_24_bytes}")

    # Sử dụng key đã hash để đảm bảo độ dài
    encrypted_otp_b64 = encrypt_otp(
        hashed_otp_key_24_bytes.decode("latin-1"), otp_data
    )  # Decode tạm để hàm nhận string
    print(f"Dữ liệu OTP gốc: {otp_data}")
    print(f"Dữ liệu OTP mã hóa (3DES ECB, Base64): {encrypted_otp_b64}")
    print("-" * 20)
