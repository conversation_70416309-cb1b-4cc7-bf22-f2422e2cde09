from datetime import datetime, timedelta

import jwt

API_KEY_SID = "SK.0.1Jl5GyuIPtlzxS5gpy6jacZw9xQYjPF1"
API_KEY_SECRET = "VUp2emJtbHhiZ2gzdlVySHhHVjd3OGhBeTQ2VWJn"


def get_access_token():
    now = datetime.utcnow()
    exp = now + timedelta(hours=1)
    exp_timestamp = int(exp.timestamp()) + 30 * 24 * 60 * 60

    header = {"cty": "stringee-api;v=1"}
    payload = {
        "jti": f"{API_KEY_SID}-{int(now.timestamp())}",
        "iss": API_KEY_SID,
        "exp": exp_timestamp,
        "rest_api": True,
    }

    token = jwt.encode(payload, API_KEY_SECRET, algorithm="HS256", headers=header)
    return token


# Ví dụ sử dụng
token = get_access_token()
print(token)
