import json
from collections import defaultdict


def build_jq_query(data):
    # Tạo một cấu trúc để lưu trữ kết quả
    result = defaultdict(dict)
    array_mappings = defaultdict(lambda: {"subfields": {}})
    objects = set()

    for item in data:
        field_source = item.get("field_source")
        field_target = item.get("field_target")
        object_name = item.get("object")
        default_value = (
            item.get("value_by_type_fixed") if item.get("value_type") == "fixed" else item.get("default_value", None)
        )

        objects.add(object_name)
        key_object_result = f"{object_name}_data"

        if "[*]" in field_target:
            # X<PERSON> lý trường hợp field_target là mảng
            try:
                array_name_target, inner_field_target = field_target.split("[*].", 1)
            except ValueError:
                raise ValueError(f"Invalid field_target format: {field_target}")

            if "[*]" in field_source:
                # Tr<PERSON><PERSON><PERSON> hợp field_source cũng là mảng
                try:
                    array_name_source, inner_field_source = field_source.split("[*].", 1)
                except ValueError:
                    raise ValueError(f"Invalid field_source format for array: {field_source}")

                # Xây dựng đường dẫn cho phần tử mảng nguồn
                source_path = f".{array_name_source}[]?.{inner_field_source}"
                # Thêm subfield vào danh sách subfields của array_name với giá trị mặc định nếu có
                if default_value is not None:
                    array_mappings[array_name_target]["subfields"][
                        inner_field_target
                    ] = f".{inner_field_target} // {json.dumps(default_value)}"
                else:
                    array_mappings[array_name_target]["subfields"][inner_field_target] = f".{inner_field_target}"
            else:
                # Trường hợp field_source không phải là mảng
                # Sử dụng giá trị cố định hoặc từ một trường đơn
                if default_value is not None:
                    value_expr = f"{json.dumps(default_value)}"
                else:
                    value_expr = f".{field_source}"

                array_mappings[array_name_target]["subfields"][inner_field_target] = value_expr

        elif "[*]" in field_source:
            # Xử lý trường hợp field_source là mảng nhưng field_target không phải mảng
            array_name_source, inner_field_source = field_source.split("[*].", 1)
            adjusted_source = f".{array_name_source}[]?.{inner_field_source}"
            if default_value is not None:
                adjusted_source += f" // {json.dumps(default_value)}"
            result[key_object_result][field_target] = adjusted_source
        else:
            # Xử lý trường hợp không phải mảng
            formatted_field_source = f".{field_source}"
            if default_value is not None:
                result[key_object_result][field_target] = f"{formatted_field_source} // {json.dumps(default_value)}"
            else:
                result[key_object_result][field_target] = formatted_field_source

    # Xây dựng biểu thức jq cho các mảng
    for array_name, mapping in array_mappings.items():
        subfields = mapping["subfields"]
        # Xây dựng phần subfields cho biểu thức jq
        subfields_jq = ", ".join([f'"{k}": {v}' for k, v in subfields.items()])
        # Xây dựng biểu thức jq hoàn chỉnh cho mảng
        # Chúng ta sẽ tạo một biểu thức như:
        # [ { "field1": .source1, "field2": .source2 // "default" } ]
        # hoặc
        # [ .array_source[] | { ... } ]
        if "[*]." in array_name:
            # Trường hợp field_source là mảng
            # Ví dụ: profile_identify[*].identify_value
            array_field = array_name.split("[*].")[0]
            jq_expr = f"[.{array_field}[] | {{ {subfields_jq} }}]"
        else:
            # Trường hợp field_source không phải mảng
            # Sẽ tạo một mảng với một đối tượng duy nhất
            jq_expr = f"[{{ {subfields_jq} }}]"
        # Thêm vào kết quả
        key_object_result = f"{object_name}_data"
        result[key_object_result][array_name] = jq_expr

    jq_query_parts = []
    for obj in objects:
        key = f"{obj}_data"
        fields = result[key]
        fields_jq = []
        for k, v in fields.items():
            fields_jq.append(f'"{k}": {v}')
        fields_jq_str = ",\n    ".join(fields_jq)
        jq_query_parts.append(f'"{key}": {{\n    {fields_jq_str}\n}}')

    jq_query = "{\n" + ",\n".join(jq_query_parts) + "\n}"

    return jq_query


def build_query_2(mapping_field):
    result = defaultdict(dict)
    array_mappings = defaultdict(lambda: {"base_path": "", "subfields": {}})

    for item in mapping_field:
        field_source = item.get("field_source")
        field_target = item.get("field_target")
        object_name = item.get("object")
        default_value = item.get("value_by_type_fixed", None)

        key_object_result = f"{object_name}_data"

        if "[*]" in field_target:
            # Xử lý trường hợp field_target là mảng
            try:
                array_name_target, _ = field_target.split("[*].")
            except ValueError:
                raise ValueError(f"Invalid field_target format: {field_target}")

            # Trích xuất phần đường dẫn sau [*] trong field_source
            try:
                field_source_split = field_source.split("[*].", 1)
                if len(field_source_split) < 2:
                    field_source_path_after_array = field_source
                else:
                    field_source_path_after_array = field_source_split[1]  # Ví dụ: "address.detail.street"
            except IndexError:
                raise ValueError(f"Invalid field_source format: {field_source}")

            # Tách remaining_path thành nested_path và field_name
            if "." in field_source_path_after_array:
                nested_path, field_name = field_source_path_after_array.rsplit(".", 1)
                # Xây dựng base_path bao gồm nested_path
                base_path = f".{field_source_split[0]}[]?.{nested_path}"
            else:
                field_name = field_source_path_after_array
                base_path = f".{field_source_split[0]}[]?"

            # Đặt base_path nếu chưa có
            if not array_mappings[array_name_target]["base_path"]:
                array_mappings[array_name_target]["base_path"] = base_path
            else:
                # Nếu đã có base_path, đảm bảo rằng nó là chính xác
                if array_mappings[array_name_target]["base_path"] != base_path:
                    raise CustomError(
                        f"Conflicting base paths for array '{array_name_target}': '{array_mappings[array_name_target]['base_path']}' and '{base_path}'"
                    )
                    # Thêm subfield vào danh sách subfields của array_name với giá trị mặc định nếu có
            if default_value is not None:
                array_mappings[array_name_target]["subfields"][
                    field_name
                ] = f".{field_name} // {json.dumps(default_value)}"
            else:
                array_mappings[array_name_target]["subfields"][field_name] = f".{field_name}"

        elif "[*]" in field_source:
            # Xử lý trường hợp field_source là mảng nhưng field_target không phải mảng
            adjusted_source = field_source.replace("[*]", "[0]?")
            result[key_object_result][field_target] = f".{adjusted_source}"
            if default_value is not None:
                result[key_object_result][field_target] = f".{adjusted_source} // {json.dumps(default_value)}"
            else:
                result[key_object_result][field_target] = f".{adjusted_source}"
        else:
            formatted_field_source = f".{field_source}"
            # Xử lý trường hợp không phải mảng
            if default_value is not None:
                result[key_object_result][field_target] = f"{formatted_field_source} // {json.dumps(default_value)}"
            else:
                result[key_object_result][field_target] = f"{formatted_field_source}"

    # Xây dựng biểu thức jq cho các mảng
    for array_name, mapping in array_mappings.items():
        base_path = mapping["base_path"]
        subfields = mapping["subfields"]
        # Xây dựng phần subfields cho biểu thức jq
        subfields_jq = ", ".join([f'"{k}": {v}' for k, v in subfields.items()])
        # Xây dựng biểu thức jq hoàn chỉnh
        jq_expr = f"{base_path}? | {{ {subfields_jq} }}"
        # Thêm vào kết quả
        result[key_object_result][array_name] = [jq_expr]

    result = dict(result)
    result = (
        json.dumps(result, indent=4, ensure_ascii=False)
        .replace('"[', "[")
        .replace(']"', "]")
        .replace('\\"', '"')
        .replace("\\n", "\n")
        .replace('}"', "}")
        .replace('"{', "{")
        .replace('".', ".")
        .replace('",', ",")
        .replace('"\n', "\n")
    )
    return result


if __name__ == "__main__":
    data = [
        {
            "field_source": "profile.cif",
            "field_source_type": "string",
            "field_target": "cif",
            "field_property": 2,
            "display_type": "multi_line",
            "value_type": "record",
            "action": "overwrite_and_ignore_value_null",
            "object": "profiles",
        },
        {
            "field_source": "profile.profile_identify[*].identify_type",
            "field_source_type": "string",
            "field_target": "profile_identify[*].identify_type",
            "field_property": 2,
            "display_type": "dropdown_single_line",
            "value_type": "record",
            "action": "overwrite_and_ignore_value_null",
            "object": "profiles",
        },
        {
            "field_source": "profile.profile_identify[*].identify_value",
            "field_source_type": "string",
            "field_target": "profile_identify[*].identify_value",
            "field_property": 2,
            "display_type": "dropdown_single_line",
            "value_type": "record",
            "action": "overwrite_and_ignore_value_null",
            "object": "profiles",
        },
        {
            "field_source": "profile.profile_identify[*].info.age",
            "field_source_type": "integer",
            "field_target": "age",
            "field_property": 1,
            "display_type": "single_line",
            "value_type": "record",
            "action": "overwrite_and_ignore_value_null",
            "object": "profiles",
            "format_value": "positive_number",
        },
        {
            "field_source": "profile.profile_identify[*].identify_type",
            "field_source_type": "string",
            "field_target": "related_to_company_v1",
            "field_property": 2,
            "display_type": "dropdown_multi_line",
            "value_type": "record",
            "action": "overwrite_and_ignore_value_null",
            "object": "profiles",
        },
    ]

    jq_query = build_query_2(data)
    with open("query.jq", "w", encoding="utf-8") as f:
        f.write(jq_query)

    print("JQ query đã được tạo và lưu vào file 'query.jq'.")
