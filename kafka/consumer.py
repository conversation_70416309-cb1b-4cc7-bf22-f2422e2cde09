import json

from confluent_kafka import Consumer, KafkaError, KafkaException

# Cấu hình consumer
conf = {
    "bootstrap.servers": "kafka1:9092",
    "group.id": "python-consumer-group",
    "auto.offset.reset": "beginning",
    "enable.auto.commit": False,  # Tắt auto commit để commit thủ công
    # 'security.protocol': 'SASL_SSL',
    # 'sasl.mechanisms': 'PLAIN',
    # 'sasl.username': 'username',
    # 'sasl.password': 'password',
}

# Tạo consumer instance
consumer = Consumer(conf)


# Callback xử lý lỗi
def error_cb(err):
    print(f"Lỗi Kafka: {err}")


# Đăng ký callback lỗi
consumer.subscribe(["test-topic"])

try:
    while True:
        msg = consumer.poll(timeout=1.0)
        print(f"msg: {msg}")
        if msg is None:
            continue

        if msg.error():
            # Xử lý lỗi
            if msg.error().code() == KafkaError._PARTITION_EOF:
                print(f"Đã đọc hết partition {msg.topic()} [{msg.partition()}]")
            else:
                raise KafkaException(msg.error())
        else:
            # Xử lý message thành công
            print(f"Received message: {msg.topic()} [{msg.partition()}] @ {msg.offset()}")
            print(f"Key: {msg.key()}")

            # Giải mã message value
            try:
                value = json.loads(msg.value().decode("utf-8"))
                print(f"Value: {value}")
            except Exception as e:
                print(f"Lỗi giải mã JSON: {str(e)}")

            # Commit offset thủ công
            consumer.commit(msg)
except KeyboardInterrupt:
    pass
finally:
    consumer.close()
