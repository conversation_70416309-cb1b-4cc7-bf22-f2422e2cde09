from confluent_kafka import Producer
import json
import time

# <PERSON><PERSON><PERSON> hình Producer
conf = {
    'bootstrap.servers': 'kafka1:9092',  # <PERSON><PERSON> sách brokers
    'client.id': 'python-producer-v1',      # Tên client định danh
    'acks': 'all',                          # <PERSON><PERSON><PERSON> bảo ghi message thành công trên tất cả replica
    'retries': 5,                           # Số lần thử lại khi gặp lỗi
    'compression.type': 'gzip',             # N<PERSON> message (none, gzip, snappy, lz4)
    # 'security.protocol': 'SASL_SSL',
    # 'sasl.mechanisms': 'PLAIN',
    # 'sasl.username': 'username',
    # 'sasl.password': 'password',
}

# Callback xác nhận message đã gửi thành công
def delivery_report(err, msg):
    if err is not None:
        print(f'Gửi message thất bại: {err}')
    else:
        print(f'Message đã gửi thành công tới [{msg.topic()}] partition [{msg.partition()}] @ offset {msg.offset()}')

# Tạo producer instance
producer = Producer(conf)

# Gửi message
try:
    for i in range(1, 6):
        # Tạo message
        message_data = {
            'id': i,
            'timestamp': int(time.time()),
            'data': f'Message số {i}'
        }
        
        # Serialize thành JSON
        value = json.dumps(message_data).encode('utf-8')
        
        # Gửi message
        producer.produce(
            topic='test-topic',
            key=str(i),  # Key dùng để xác định partition
            value=value,
            callback=delivery_report  # Đăng ký callback
        )
        
        # Poll để xử lý các event và callback
        producer.poll(0)
        
        time.sleep(1)

except KeyboardInterrupt:
    print("Dừng bởi người dùng")
except Exception as e:
    print(f"Lỗi khi gửi message: {str(e)}")
finally:
    # Đảm bảo tất cả message đã được gửi trước khi đóng
    pass