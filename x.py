import json
from collections import defaultdict


def build_jq_query(data):
    # <PERSON><PERSON><PERSON>m dữ liệu theo đối tượng
    objects = defaultdict(lambda: {"fields": {}, "arrays": defaultdict(lambda: {"source_path": "", "subfields": {}})})

    for item in data:
        field_source = item.get("field_source")
        field_target = item.get("field_target")
        object_name = item.get("object")
        value_type = item.get("value_type")
        default_value = item.get("value_by_type_fixed") if value_type == "fixed" else item.get("default_value", None)

        if "[*]" in field_target:
            # Trường hợp field_target là mảng
            try:
                array_name_target, inner_field_target = field_target.split("[*].", 1)
            except ValueError:
                raise ValueError(f"Invalid field_target format: {field_target}")

            if "[*]" in field_source:
                # Trường hợp field_source cũng là mảng
                try:
                    array_name_source, inner_field_source = field_source.split("[*].", 1)
                except ValueError:
                    raise ValueError(f"Invalid field_source format for array: {field_source}")

                # <PERSON><PERSON><PERSON>h<PERSON> đ<PERSON>ờng dẫn nguồn
                source_path = f".{array_name_source}"

                # Xử lý biểu thức subfield
                if value_type == "fixed":
                    if isinstance(default_value, str):
                        subfield_expr = f'"{default_value}"'
                    else:
                        subfield_expr = f"{default_value}"
                else:
                    if default_value is not None:
                        subfield_expr = f".{inner_field_target} // {json.dumps(default_value)}"
                    else:
                        subfield_expr = f".{inner_field_target}"

                # Gán biểu thức subfield
                objects[object_name]["arrays"][array_name_target]["subfields"][inner_field_target] = subfield_expr

                # Lưu đường dẫn nguồn
                if not objects[object_name]["arrays"][array_name_target]["source_path"]:
                    objects[object_name]["arrays"][array_name_target]["source_path"] = f".{array_name_source}"
            else:
                # Trường hợp field_source không phải mảng
                if value_type == "fixed":
                    if isinstance(default_value, str):
                        value_expr = f'"{default_value}"'
                    else:
                        value_expr = f"{default_value}"
                else:
                    if default_value is not None:
                        value_expr = f".{field_source} // {json.dumps(default_value)}"
                    else:
                        value_expr = f".{field_source}"

                # Gán biểu thức subfield
                objects[object_name]["arrays"][array_name_target]["subfields"][inner_field_target] = value_expr

                # Đặt đường dẫn nguồn trống (fixed mapping)
                objects[object_name]["arrays"][array_name_target]["source_path"] = ""
        else:
            # Trường hợp field_target không phải mảng
            if "[*]" in field_source:
                # Trường hợp field_source là mảng
                try:
                    array_name_source, inner_field_source = field_source.split("[*].", 1)
                except ValueError:
                    raise ValueError(f"Invalid field_source format for array: {field_source}")

                # Định nghĩa đường dẫn nguồn
                adjusted_source = f".{array_name_source}[]?.{inner_field_source}"

                if value_type == "fixed":
                    if isinstance(default_value, str):
                        adjusted_source = f'"{default_value}"'
                    else:
                        adjusted_source = f"{default_value}"
                elif default_value is not None:
                    adjusted_source += f" // {json.dumps(default_value)}"

                # Gán biểu thức vào trường đơn
                objects[object_name]["fields"][field_target] = adjusted_source
            else:
                # Trường hợp cả field_source và field_target đều không phải mảng
                field_expr = f".{field_source}"
                if value_type == "fixed":
                    if isinstance(default_value, str):
                        field_expr = f'"{default_value}"'
                    else:
                        field_expr = f"{default_value}"
                elif default_value is not None:
                    field_expr = f"{field_expr} // {json.dumps(default_value)}"

                # Gán biểu thức vào trường đơn
                objects[object_name]["fields"][field_target] = field_expr

    jq_parts = []
    for object_name, content in objects.items():
        object_key = f"{object_name}_data"
        jq_parts.append(f'    "{object_key}": {{')

        field_lines = []

        # Xử lý các trường không phải mảng
        for field_target, field_expr in content["fields"].items():
            field_lines.append(f'        "{field_target}": {field_expr}')

        # Xử lý các trường là mảng
        for array_name, array_content in content["arrays"].items():
            source_path = array_content["source_path"]
            subfields = array_content["subfields"]

            if source_path:
                # Trường hợp nguồn là mảng
                subfields_jq = ", ".join([f'"{k}": {v}' for k, v in subfields.items()])
                array_jq = f'        "{array_name}": [ {source_path}[]? | {{ {subfields_jq} }} ]'
            else:
                # Trường hợp nguồn không phải mảng (fixed mapping)
                subfields_jq = ", ".join([f'"{k}": {v}' for k, v in subfields.items()])
                array_jq = f'        "{array_name}": [{{ {subfields_jq} }}]'

            field_lines.append(array_jq)

        # Kết hợp các trường với dấu phẩy
        fields_str = ",\n".join(field_lines)
        jq_parts.append(fields_str)
        jq_parts.append(f"    }}")  # Đóng đối tượng

    # Kết hợp tất cả các đối tượng vào một biểu thức jq
    jq_query = "{\n" + ",\n".join(jq_parts) + "\n}"
    jq_query = jq_query.replace("{,", "{")
    return jq_query


if __name__ == "__main__":
    data = [
        {
            "field_source": "name",
            "field_source_type": "string",
            "field_target": "name",
            "field_property": 2,
            "display_type": "single_line",
            "value_type": "record",
            "action": "overwrite_and_ignore_value_null",
            "object": "profiles",
        },
        {
            "field_source": "age",
            "field_source_type": "integer",
            "field_target": "age",
            "field_property": 1,
            "display_type": "single_line",
            "value_type": "record",
            "action": "overwrite_and_ignore_value_null",
            "object": "profiles",
            "format_value": "positive_number",
        },
        {
            "field_source": "cif",
            "field_source_type": "string",
            "field_target": "cif",
            "field_property": 2,
            "display_type": "multi_line",
            "value_type": "record",
            "action": "overwrite_and_ignore_value_null",
            "object": "profiles",
        },
        {
            "field_source": "gtdd",
            "field_source_type": "string",
            "field_target": "profile_identify[*].identify_value",
            "field_property": 2,
            "display_type": "dropdown_single_line",
            "value_type": "record",
            "action": "overwrite_and_ignore_value_null",
            "object": "profiles",
        },
        {
            "field_source": "address.type",
            "field_source_type": "string",
            "field_target": "address_personal[*].type_upload",
            "field_property": 2,
            "display_type": "dropdown_single_line",
            "value_type": "record",
            "action": "overwrite_and_ignore_value_null",
            "object": "profiles",
        },
        {
            "field_source": "address.detail",
            "field_source_type": "string",
            "field_target": "address_personal[*].detail_upload",
            "field_property": 2,
            "display_type": "dropdown_single_line",
            "value_type": "record",
            "action": "overwrite_and_ignore_value_null",
            "object": "profiles",
        },
        {
            "field_source": "default_value",
            "field_source_type": "string",
            "field_target": "address_personal[*].district_upload",
            "field_property": 2,
            "display_type": "dropdown_single_line",
            "value_type": "fixed",
            "action": "overwrite_and_ignore_value_null",
            "object": "profiles",
            "value_by_type_fixed": "Hà Nội",
        },
        {
            "field_source": "default_value",
            "field_source_type": "string",
            "field_target": "address_personal[*].city_upload",
            "field_property": 2,
            "display_type": "dropdown_single_line",
            "value_type": "fixed",
            "action": "overwrite_and_ignore_value_null",
            "object": "profiles",
            "value_by_type_fixed": "Hà Nội",
        },
        {
            "field_source": "default_value",
            "field_source_type": "string",
            "field_target": "address_personal[*].country_upload",
            "field_property": 2,
            "display_type": "dropdown_single_line",
            "value_type": "fixed",
            "action": "overwrite_and_ignore_value_null",
            "object": "profiles",
            "value_by_type_fixed": "Việt Nam",
        },
        {
            "field_source": "default_value",
            "field_source_type": "string",
            "field_target": "address_personal[*].county_upload",
            "field_property": 2,
            "display_type": "dropdown_single_line",
            "value_type": "fixed",
            "action": "overwrite_and_ignore_value_null",
            "object": "profiles",
            "value_by_type_fixed": "Hà Nội",
        },
        {
            "field_source": "default_value",
            "field_source_type": "string",
            "field_target": "profile_identify[*].identify_type",
            "field_property": 2,
            "display_type": "dropdown_single_line",
            "value_type": "fixed",
            "action": "overwrite_and_ignore_value_null",
            "object": "profiles",
            "value_by_type_fixed": "citizen_identity",
        },
    ]

    jq_query = build_jq_query(data)
    with open("query.jq", "w", encoding="utf-8") as f:
        f.write(jq_query)

    print("JQ query đã được tạo và lưu vào file 'query.jq'.")
