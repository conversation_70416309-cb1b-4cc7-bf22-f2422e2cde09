import boto3
from botocore.exceptions import ClientError


# Hàm lấy danh sách các vùng trong AWS
def get_aws_regions():
    try:
        # Khởi tạo EC2 client với vùng mặc định (us-east-1)
        ec2_client = boto3.client("ec2", region_name="us-east-1")

        # Gọi API DescribeRegions
        response = ec2_client.describe_regions(AllRegions=True)

        # Trích xuất danh sách vùng
        regions = response["Regions"]

        # In danh sách vùng theo định dạng bảng
        print("\n{:<20} {:<30} {:<20}".format("Region Name", "Endpoint", "OptInStatus"))
        print("-" * 70)
        for region in regions:
            print("{:<20} {:<30} {:<20}".format(region["RegionName"], region["Endpoint"], region["OptInStatus"]))

        # Trả về danh sách tên vùng
        region_names = [region["RegionName"] for region in regions]
        return region_names

    except ClientError as e:
        print(f"Đã xảy ra lỗi khi gọi API: {e}")
        return None
    except Exception as e:
        print(f"Đã xảy ra lỗi không xác định: {e}")
        return None


# Gọi hàm và in kết quả
if __name__ == "__main__":
    regions = get_aws_regions()
    if regions:
        print(f"\nTổng số vùng: {len(regions)}")
