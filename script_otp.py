import base64
import os  # Thêm thư viện os để tạo key ngẫu nhiên cho ví dụ

from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import padding
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes


def encrypt_tripledes_ecb_pkcs7(encryption_key: str, to_encrypt: str) -> str:
    """
    Mã hóa một chuỗi sử dụng TripleDES với chế độ ECB và padding PKCS7,
    trả về kết quả dưới dạng chuỗi Base64.

    Args:
        encryption_key: <PERSON>h<PERSON><PERSON> mã hóa (chuỗi). Phải có độ dài khi mã hóa UTF-8 là 16 hoặc 24 byte.
        to_encrypt: Chuỗi cần mã hóa.

    Returns:
        Chuỗi Base64 chứa dữ liệu đã mã hóa.

    Raises:
        ValueError: Nếu độ dài khóa không hợp lệ.
        Exception: <PERSON>ế<PERSON> có lỗi trong quá trình mã hóa.
    """
    try:
        # 1. Chuyển đổi khóa và dữ liệu sang bytes (sử dụng UTF-8)
        key_bytes = encryption_key.encode("utf-8")
        data_bytes = to_encrypt.encode("utf-8")

        # TripleDES yêu cầu độ dài khóa là 16 (cho 2-key 3DES) hoặc 24 bytes (cho 3-key 3DES)
        if len(key_bytes) not in [16, 24]:
            raise ValueError("TripleDES key must be 16 or 24 bytes long when encoded in UTF-8.")

        # 2. Chọn backend mặc định
        backend = default_backend()

        # 3. Tạo đối tượng thuật toán TripleDES
        algorithm = algorithms.TripleDES(key_bytes)

        # 4. Tạo đối tượng chế độ mã hóa ECB
        mode = modes.ECB()

        # 5. Tạo đối tượng Cipher
        cipher = Cipher(algorithm, mode, backend=backend)

        # 6. Tạo đối tượng Encryptor
        encryptor = cipher.encryptor()

        # 7. Áp dụng padding PKCS7
        # TripleDES có kích thước khối là 64 bit (8 bytes)
        padder = padding.PKCS7(algorithms.TripleDES.block_size).padder()
        padded_data = padder.update(data_bytes) + padder.finalize()

        # 8. Thực hiện mã hóa
        encrypted_data = encryptor.update(padded_data) + encryptor.finalize()

        # 9. Chuyển đổi dữ liệu đã mã hóa sang chuỗi Base64
        base64_encoded = base64.b64encode(encrypted_data).decode("utf-8")

        return base64_encoded

    except ValueError as ve:
        print(f"Lỗi về khóa: {ve}")
        raise  # Re-raise the exception after printing
    except Exception as e:
        print(f"Lỗi mã hóa: {e}")
        raise  # Re-raise the exception after printing


# --- Ví dụ sử dụng ---

# Khóa mẫu (cần đảm bảo sau khi mã hóa UTF-8 có độ dài 16 hoặc 24 bytes)
# Ví dụ: khóa 24 byte (3-key 3DES)
# Sử dụng os.urandom để tạo khóa ngẫu nhiên cho mục đích testing an toàn hơn
# Trong thực tế, bạn sẽ lấy khóa từ nguồn cấu hình an toàn
# sample_key = os.urandom(24) # Khóa ngẫu nhiên 24 bytes
# Hoặc dùng một chuỗi cố định có độ dài tương ứng khi mã hóa UTF-8
# (Đảm bảo chuỗi này đủ phức tạp và được quản lý an toàn)
sample_key_string = (
    "ThisIsAStrongKeyFor3DES!"  # Chuỗi này có 24 ký tự, nếu mỗi ký tự là 1 byte trong UTF-8 thì đủ 24 byte
)

# Chuỗi cần mã hóa
text_to_encrypt = "Xin chào, đây là một thông điệp bí mật!"

try:
    # Kiểm tra độ dài khóa mẫu
    if len(sample_key_string.encode("utf-8")) == 24:
        encrypted_string = encrypt_tripledes_ecb_pkcs7(sample_key_string, text_to_encrypt)
        print(f"Chuỗi cần mã hóa: {text_to_encrypt}")
        print(f"Khóa sử dụng: {sample_key_string}")
        print(f"Chuỗi sau khi mã hóa (Base64): {encrypted_string}")

        # (Tùy chọn) Thử giải mã để kiểm tra
        import base64

        from cryptography.hazmat.backends import default_backend
        from cryptography.hazmat.primitives import padding
        from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes

        def decrypt_tripledes_ecb_pkcs7(encryption_key: str, to_decrypt_base64: str) -> str:
            try:
                key_bytes = encryption_key.encode("utf-8")
                if len(key_bytes) not in [16, 24]:
                    raise ValueError("TripleDES key must be 16 or 24 bytes long when encoded in UTF-8.")

                encrypted_data = base64.b64decode(to_decrypt_base64)

                backend = default_backend()
                algorithm = algorithms.TripleDES(key_bytes)
                mode = modes.ECB()
                cipher = Cipher(algorithm, mode, backend=backend)
                decryptor = cipher.decryptor()

                # Thực hiện giải mã
                decrypted_padded_data = decryptor.update(encrypted_data) + decryptor.finalize()

                # Loại bỏ padding
                unpadder = padding.PKCS7(algorithms.TripleDES.block_size).unpadder()
                decrypted_data = unpadder.update(decrypted_padded_data) + unpadder.finalize()

                return decrypted_data.decode("utf-8")

            except ValueError as ve:
                print(f"Lỗi về khóa hoặc dữ liệu Base64: {ve}")
                raise
            except Exception as e:
                print(f"Lỗi giải mã: {e}")
                raise

        decrypted_string = decrypt_tripledes_ecb_pkcs7(sample_key_string, encrypted_string)
        print(f"Chuỗi sau khi giải mã: {decrypted_string}")

    else:
        print(
            f"Lỗi: Chuỗi khóa mẫu '{sample_key_string}' sau khi mã hóa UTF-8 có độ dài {len(sample_key_string.encode('utf-8'))}, không phải 16 hoặc 24 byte."
        )


except Exception as e:
    print(f"Chương trình gặp lỗi: {e}")
